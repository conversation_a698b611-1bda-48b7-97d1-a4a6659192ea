// dump_sockets.js
const MAX = 256;
['send', 'recv'].forEach(n => {
    const funcPtr = Module.getExportByName(null, n);
    Interceptor.attach(funcPtr, {
        onEnter(args) {
            this.buf = args[1];
            this.len = args[2].toInt32();
            this.socket = args[0].toInt32();
            this.type = n;
        },
        onLeave(retval) {
            const bytes = retval.toInt32();
            if (bytes > 0 && this.type === 'recv') {
                console.log(`\n[<-- RECV on socket ${this.socket} | ${bytes} bytes]`);
                console.log(hexdump(this.buf, {
                    length: Math.min(bytes, MAX),
                    header: true,
                    ansi: true
                }));
            }
        }
    });
});
